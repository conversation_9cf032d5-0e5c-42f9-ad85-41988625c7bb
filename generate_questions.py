#!/usr/bin/env python3
import json
import csv
import os
from collections import defaultdict

def load_annotations():
    """加载标注数据"""
    annotations = []
    with open('sample/meta/annotations.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            annotations.append(row)
    return annotations

def generate_questions(annotations):
    """为每个音频文件生成开放式问题"""
    
    # 按文件ID分组标注
    files_data = defaultdict(list)
    for ann in annotations:
        files_data[ann['file_id']].append(ann)
    
    questions = []
    
    # 问题模板
    question_templates = {
        'Active': {
            'questions': [
                "Based on the acoustic characteristics of this bee colony audio, analyze the activity level and behavioral patterns. What specific acoustic features indicate the colony's current state?",
                "Listen to this bee colony recording and identify the dominant frequency patterns. What do these patterns suggest about the bees' collective behavior and activity level?",
                "Analyze the temporal dynamics in this bee colony audio. How do the sound intensity and frequency variations reflect the bees' group activities?"
            ],
            'answers': [
                "The audio shows high activity levels with consistent buzzing patterns, indicating active foraging, communication, and normal colony operations.",
                "Active colonies typically exhibit broad frequency spectra with sustained high-frequency components and rhythmic variations indicating coordinated group behavior.",
                "The temporal patterns show sustained activity with periodic intensity variations, characteristic of an actively functioning bee colony."
            ]
        },
        'Missing_Queen': {
            'questions': [
                "Examine this bee colony audio for signs of distress or abnormal behavior. What acoustic indicators suggest potential issues with colony leadership or structure?",
                "Analyze the frequency and amplitude patterns in this recording. How do they differ from what you would expect in a normal, healthy colony?",
                "Listen for specific acoustic signatures that might indicate colony stress or organizational problems. What behavioral patterns can you identify?"
            ],
            'answers': [
                "The audio exhibits irregular patterns, increased agitation sounds, and potential distress calls indicating the absence of queen pheromones and leadership.",
                "Missing queen colonies show erratic frequency patterns, increased high-frequency agitation sounds, and less coordinated acoustic behavior.",
                "The recording demonstrates signs of colony disorganization with irregular buzzing patterns and potential alarm pheromone responses."
            ]
        },
        'QueenBee': {
            'questions': [
                "Analyze this bee colony recording for indicators of healthy queen presence. What acoustic features suggest proper colony hierarchy and organization?",
                "Examine the harmonic structure and rhythmic patterns in this audio. How do they reflect the influence of queen pheromones on colony behavior?",
                "Listen for specific acoustic signatures that indicate a well-organized colony with proper leadership. What patterns support this assessment?"
            ],
            'answers': [
                "The audio shows organized, rhythmic patterns with stable frequency distributions, indicating healthy queen presence and proper colony hierarchy.",
                "Queen-right colonies exhibit more coordinated acoustic patterns with stable harmonic structures and less agitation, reflecting pheromonal influence.",
                "The recording demonstrates organized collective behavior with consistent buzzing patterns characteristic of a queen-led colony."
            ]
        },
        'NO_QueenBee': {
            'questions': [
                "Evaluate this bee colony audio for signs of queenless behavior. What specific acoustic characteristics indicate the absence of queen influence?",
                "Analyze the stress indicators and behavioral patterns in this recording. How do they reflect the colony's response to queen loss?",
                "Examine the frequency distribution and temporal patterns. What features distinguish this from a queen-right colony?"
            ],
            'answers': [
                "The audio exhibits signs of queenless behavior including increased agitation, irregular patterns, and potential emergency queen cell construction sounds.",
                "Queenless colonies show elevated stress indicators with more chaotic acoustic patterns and increased high-frequency components indicating anxiety.",
                "The recording lacks the organized patterns of queen-right colonies, showing instead erratic buzzing and potential supersedure behaviors."
            ]
        },
        'Swarming': {
            'questions': [
                "Analyze this swarming bee colony audio for pre-swarm and swarm behavior indicators. What acoustic features characterize this critical colony event?",
                "Examine the intensity and frequency patterns during swarming. How do they reflect the complex behavioral dynamics of colony reproduction?",
                "Listen for specific acoustic signatures of swarming behavior. What patterns indicate preparation for or active swarming?"
            ],
            'answers': [
                "Swarming audio shows characteristic high-intensity buzzing with specific frequency patterns indicating mass bee movement and excitement.",
                "The recording exhibits elevated activity levels with distinctive acoustic signatures of coordinated mass movement and preparation behaviors.",
                "Swarming behavior produces unique acoustic patterns with increased amplitude and specific frequency characteristics of collective excitement."
            ]
        }
    }
    
    for file_id, file_annotations in files_data.items():
        # 获取文件信息
        first_ann = file_annotations[0]
        category = first_ann['category']
        source_file = first_ann['new_file']
        
        # 选择问题模板
        if category in question_templates:
            template = question_templates[category]
            import random
            question = random.choice(template['questions'])
            answer = random.choice(template['answers'])
        else:
            # 默认问题
            question = "Analyze this bee colony audio recording. What can you determine about the colony's behavioral state and activity patterns based on the acoustic characteristics?"
            answer = "The audio provides insights into the colony's behavioral state through frequency patterns, intensity variations, and temporal dynamics that reflect the bees' collective activities."
        
        # 生成解释
        explanation = f"This audio sample represents a {category.replace('_', ' ').lower()} bee colony state. "
        
        if category == 'Active':
            explanation += "Active colonies exhibit consistent buzzing patterns with coordinated frequency variations indicating normal foraging, communication, and maintenance activities."
        elif category == 'Missing_Queen':
            explanation += "The absence of queen pheromones leads to increased agitation and disorganized acoustic patterns as workers search for leadership cues."
        elif category == 'QueenBee':
            explanation += "Queen-right colonies demonstrate organized acoustic behavior with stable patterns reflecting the calming influence of queen pheromones."
        elif category == 'NO_QueenBee':
            explanation += "Queenless colonies show stress indicators and irregular patterns as the colony attempts to address the leadership vacuum."
        elif category == 'Swarming':
            explanation += "Swarming represents a critical reproductive event with distinctive high-intensity acoustic signatures of mass coordinated movement."
        
        # 创建问题对象
        question_obj = {
            "id": file_id,
            "skill": "Collective biological audio activity recognition and analysis",
            "source_file": source_file,
            "question": question,
            "correct_answers": answer,
            "explanation": explanation
        }
        
        questions.append(question_obj)
    
    return questions

def save_questions_json(questions):
    """保存问题到JSON文件"""
    json_path = 'sample/meta/questions.json'
    
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(questions, f, indent=2, ensure_ascii=False)
    
    print(f"问题已保存到: {json_path}")
    print(f"总共生成了 {len(questions)} 个问题")

def main():
    print("开始生成开放式问题...")
    
    # 加载标注数据
    annotations = load_annotations()
    print(f"加载了 {len(annotations)} 条标注记录")
    
    # 生成问题
    questions = generate_questions(annotations)
    
    # 保存JSON
    save_questions_json(questions)
    
    print("问题生成完成！")

if __name__ == "__main__":
    main()
