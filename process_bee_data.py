#!/usr/bin/env python3
import os
import shutil
import csv
import json
import random
from collections import defaultdict

def parse_mlf_file(mlf_path):
    """解析MLF标注文件"""
    annotations = {}
    current_file = None
    
    with open(mlf_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line == '.':
                current_file = None
                continue
                
            # 检查是否是文件名行（不包含数字开头）
            if not line[0].isdigit() and current_file is None:
                current_file = line
                annotations[current_file] = []
            elif current_file and line[0].isdigit():
                # 解析时间标注行
                parts = line.split()
                if len(parts) >= 3:
                    start_time = float(parts[0])
                    end_time = float(parts[1])
                    label = parts[2]
                    annotations[current_file].append({
                        'start': start_time,
                        'end': end_time,
                        'label': label
                    })
    
    return annotations

def categorize_files():
    """按类别分组音频文件"""
    categories = {
        'Active': [],
        'Missing_Queen': [],
        'QueenBee': [],
        'NO_QueenBee': [],
        'Swarming': []
    }
    
    # 获取所有音频文件
    audio_files = []
    for ext in ['*.wav', '*.mp3']:
        import glob
        audio_files.extend(glob.glob(ext))
    
    for file in audio_files:
        filename = os.path.basename(file)
        
        if 'Active' in filename:
            categories['Active'].append(file)
        elif 'Missing Queen' in filename:
            categories['Missing_Queen'].append(file)
        elif 'QueenBee' in filename and 'NO_QueenBee' not in filename:
            categories['QueenBee'].append(file)
        elif 'NO_QueenBee' in filename:
            categories['NO_QueenBee'].append(file)
        elif 'Swarming' in filename:
            categories['Swarming'].append(file)
    
    return categories

def select_samples(categories, total_samples=30):
    """从每个类别中选择样本"""
    selected = {}
    
    # 计算每个类别应该选择的样本数
    category_counts = {k: len(v) for k, v in categories.items() if v}
    total_files = sum(category_counts.values())
    
    for category, files in categories.items():
        if not files:
            continue
            
        # 按比例分配，但至少每个类别选1个，最多选10个
        proportion = len(files) / total_files
        target_count = max(1, min(10, int(total_samples * proportion)))
        
        # 随机选择文件
        selected[category] = random.sample(files, min(target_count, len(files)))
    
    # 确保总数不超过30
    all_selected = []
    for files in selected.values():
        all_selected.extend(files)
    
    if len(all_selected) > total_samples:
        all_selected = random.sample(all_selected, total_samples)
        # 重新分配到类别
        selected = {k: [] for k in selected.keys()}
        for file in all_selected:
            for category, files in categories.items():
                if file in files:
                    selected[category].append(file)
                    break
    
    return selected

def copy_audio_files(selected_files):
    """复制选中的音频文件到sample/audio目录"""
    copied_files = []
    file_counter = 1
    
    for category, files in selected_files.items():
        for file_path in files:
            # 生成新的文件名
            ext = os.path.splitext(file_path)[1]
            new_filename = f"{file_counter:03d}_{category}{ext}"
            new_path = os.path.join('sample/audio', new_filename)
            
            # 复制文件
            shutil.copy2(file_path, new_path)
            
            copied_files.append({
                'id': f"{file_counter:03d}",
                'original_file': os.path.basename(file_path),
                'new_file': new_filename,
                'category': category,
                'file_path': new_path
            })
            
            file_counter += 1
    
    return copied_files

def extract_annotations(copied_files, annotations):
    """提取对应的标注信息"""
    csv_data = []
    
    for file_info in copied_files:
        original_name = os.path.splitext(file_info['original_file'])[0]
        
        # 在标注中查找对应的条目
        file_annotations = None
        for ann_key, ann_data in annotations.items():
            if original_name in ann_key or ann_key in original_name:
                file_annotations = ann_data
                break
        
        if file_annotations:
            for ann in file_annotations:
                csv_data.append({
                    'file_id': file_info['id'],
                    'original_file': file_info['original_file'],
                    'new_file': file_info['new_file'],
                    'category': file_info['category'],
                    'start_time': ann['start'],
                    'end_time': ann['end'],
                    'label': ann['label']
                })
        else:
            # 如果没有找到标注，添加一个默认条目
            csv_data.append({
                'file_id': file_info['id'],
                'original_file': file_info['original_file'],
                'new_file': file_info['new_file'],
                'category': file_info['category'],
                'start_time': 0,
                'end_time': 0,
                'label': 'unknown'
            })
    
    return csv_data

def save_annotations_csv(csv_data):
    """保存标注到CSV文件"""
    csv_path = 'sample/meta/annotations.csv'
    
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        fieldnames = ['file_id', 'original_file', 'new_file', 'category', 'start_time', 'end_time', 'label']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_data)
    
    print(f"标注信息已保存到: {csv_path}")

def main():
    print("开始处理蜜蜂音频数据...")
    
    # 解析MLF标注文件
    print("解析标注文件...")
    annotations = parse_mlf_file('beeAnnotations.mlf')
    print(f"解析了 {len(annotations)} 个文件的标注")
    
    # 按类别分组文件
    print("按类别分组音频文件...")
    categories = categorize_files()
    for category, files in categories.items():
        print(f"{category}: {len(files)} 个文件")
    
    # 选择样本
    print("选择代表性样本...")
    selected_files = select_samples(categories, 30)
    total_selected = sum(len(files) for files in selected_files.values())
    print(f"总共选择了 {total_selected} 个文件")
    
    for category, files in selected_files.items():
        if files:
            print(f"  {category}: {len(files)} 个文件")
    
    # 复制音频文件
    print("复制音频文件...")
    copied_files = copy_audio_files(selected_files)
    
    # 提取标注信息
    print("提取标注信息...")
    csv_data = extract_annotations(copied_files, annotations)
    
    # 保存CSV
    save_annotations_csv(csv_data)
    
    print("处理完成！")
    return copied_files

if __name__ == "__main__":
    copied_files = main()
